# Playify - Real-Time Collaborative Music Playlist Application

A modern web application that allows multiple participants to collaborate on a music playlist in real-time during social events. Built with Next.js, Socket.IO, Supabase, and the Spotify Web API.

## Features

### Core Functionality
- **Real-time Collaboration**: Multiple users can add tracks and vote simultaneously
- **Host Controls**: Exclusive playback controls for session hosts
- **Democratic Voting**: Emoji-based voting system (👍, ❤️, 🔥, 👎, 😴)
- **Smart Duplicate Handling**: Automatically increases priority instead of adding duplicates
- **QR Code Access**: Easy session joining via QR codes or 6-character codes

### User Experience
- **No Account Required**: Participants join with just a display name
- **Responsive Design**: Works seamlessly on desktop and mobile
- **Real-time Updates**: Instant playlist updates across all devices
- **Gamification**: Points system and achievements for engagement

### Technical Features
- **Spotify Integration**: Search, preview, and export playlists
- **WebSocket Communication**: Real-time updates via Socket.IO
- **Scalable Architecture**: Handles 5-100+ simultaneous users per session
- **Modern Stack**: Next.js 14, TypeScript, Tailwind CSS, Zustand

## Tech Stack

- **Frontend**: Next.js 14, React, TypeScript, Tailwind CSS
- **State Management**: Zustand
- **Real-time**: Socket.IO
- **Database**: Supabase (PostgreSQL)
- **Music API**: Spotify Web API
- **Icons**: Lucide React
- **QR Codes**: qrcode library

## Prerequisites

Before running this application, you need:

1. **Node.js** (v18 or higher)
2. **Spotify Developer Account** and App
3. **Supabase Account** and Project

## Setup Instructions

### 1. Clone and Install Dependencies

```bash
git clone <repository-url>
cd playify-app
npm install
```

### 2. Spotify API Setup

1. Go to [Spotify Developer Dashboard](https://developer.spotify.com/dashboard)
2. Create a new app
3. Note your Client ID and Client Secret
4. Add redirect URIs if needed for user authentication

### 3. Supabase Setup

1. Create a new project at [Supabase](https://supabase.com)
2. Go to Settings > API to get your URL and anon key
3. Run the database schema:
   - Go to SQL Editor in Supabase dashboard
   - Copy and run the contents of `database/schema.sql`

### 4. Environment Configuration

Create a `.env.local` file in the root directory:

```env
# Spotify API Configuration
SPOTIFY_CLIENT_ID=your_spotify_client_id
SPOTIFY_CLIENT_SECRET=your_spotify_client_secret
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### 5. Run the Application

```bash
npm run dev
```

The application will be available at `http://localhost:3000`

## Usage

### For Hosts

1. Click "Create Session" on the homepage
2. Enter your name and session details
3. Configure session settings (max participants, chat, etc.)
4. Share the session code or QR code with participants
5. Control playback and moderate the session

### For Participants

1. Click "Join Session" on the homepage
2. Enter the 6-character session code
3. Provide your display name
4. Start adding tracks and voting!

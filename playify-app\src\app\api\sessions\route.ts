import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { generateSessionCode, generateId } from '@/lib/utils';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { hostName, sessionName, settings } = body;
    
    if (!hostName || !sessionName) {
      return NextResponse.json(
        { error: 'Host name and session name are required' },
        { status: 400 }
      );
    }
    
    const sessionCode = generateSessionCode();
    const hostId = generateId();
    const sessionId = generateId();
    
    // Create session
    const { data: session, error: sessionError } = await supabase
      .from('sessions')
      .insert({
        id: sessionId,
        code: sessionCode,
        host_id: hostId,
        name: sessionName,
        is_active: true,
        is_locked: false,
        max_participants: settings?.maxParticipants || 50,
        current_participants: 1,
        settings: settings || {
          allowChat: true,
          maxPlaylistLength: 100,
          voteWeight: 1,
          autoSkipThreshold: -5,
          requireApproval: false,
          lowTrackThreshold: 5,
        },
      })
      .select()
      .single();
    
    if (sessionError) {
      console.error('Error creating session:', sessionError);
      return NextResponse.json(
        { error: 'Failed to create session' },
        { status: 500 }
      );
    }
    
    // Add host as participant
    const { error: participantError } = await supabase
      .from('participants')
      .insert({
        id: generateId(),
        session_id: sessionId,
        user_id: hostId,
        display_name: hostName,
        is_host: true,
        points: 0,
        achievements: [],
      });
    
    if (participantError) {
      console.error('Error adding host as participant:', participantError);
      // Clean up session if participant creation fails
      await supabase.from('sessions').delete().eq('id', sessionId);
      return NextResponse.json(
        { error: 'Failed to create session' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({
      session: {
        id: session.id,
        code: session.code,
        hostId: session.host_id,
        name: session.name,
        isActive: session.is_active,
        isLocked: session.is_locked,
        maxParticipants: session.max_participants,
        currentParticipants: session.current_participants,
        createdAt: new Date(session.created_at),
        settings: session.settings,
      },
      hostUser: {
        id: hostId,
        displayName: hostName,
        isHost: true,
        joinedAt: new Date(),
        points: 0,
        achievements: [],
      },
    });
  } catch (error) {
    console.error('Error creating session:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const code = searchParams.get('code');
    
    if (!code) {
      return NextResponse.json(
        { error: 'Session code is required' },
        { status: 400 }
      );
    }
    
    const { data: session, error } = await supabase
      .from('sessions')
      .select('*')
      .eq('code', code)
      .eq('is_active', true)
      .single();
    
    if (error || !session) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      session: {
        id: session.id,
        code: session.code,
        hostId: session.host_id,
        name: session.name,
        isActive: session.is_active,
        isLocked: session.is_locked,
        maxParticipants: session.max_participants,
        currentParticipants: session.current_participants,
        createdAt: new Date(session.created_at),
        settings: session.settings,
      },
    });
  } catch (error) {
    console.error('Error fetching session:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Database schema types
export interface Database {
  public: {
    Tables: {
      sessions: {
        Row: {
          id: string;
          code: string;
          host_id: string;
          name: string;
          is_active: boolean;
          is_locked: boolean;
          max_participants: number;
          current_participants: number;
          created_at: string;
          settings: any;
        };
        Insert: {
          id?: string;
          code: string;
          host_id: string;
          name: string;
          is_active?: boolean;
          is_locked?: boolean;
          max_participants?: number;
          current_participants?: number;
          created_at?: string;
          settings?: any;
        };
        Update: {
          id?: string;
          code?: string;
          host_id?: string;
          name?: string;
          is_active?: boolean;
          is_locked?: boolean;
          max_participants?: number;
          current_participants?: number;
          created_at?: string;
          settings?: any;
        };
      };
      participants: {
        Row: {
          id: string;
          session_id: string;
          user_id: string;
          display_name: string;
          is_host: boolean;
          joined_at: string;
          points: number;
          achievements: string[];
        };
        Insert: {
          id?: string;
          session_id: string;
          user_id: string;
          display_name: string;
          is_host?: boolean;
          joined_at?: string;
          points?: number;
          achievements?: string[];
        };
        Update: {
          id?: string;
          session_id?: string;
          user_id?: string;
          display_name?: string;
          is_host?: boolean;
          joined_at?: string;
          points?: number;
          achievements?: string[];
        };
      };
      tracks: {
        Row: {
          id: string;
          session_id: string;
          spotify_id: string;
          name: string;
          artist: string;
          album: string;
          duration: number;
          preview_url: string | null;
          image_url: string | null;
          added_by: string;
          added_at: string;
          total_score: number;
          is_playing: boolean;
          has_played: boolean;
          position: number;
        };
        Insert: {
          id?: string;
          session_id: string;
          spotify_id: string;
          name: string;
          artist: string;
          album: string;
          duration: number;
          preview_url?: string | null;
          image_url?: string | null;
          added_by: string;
          added_at?: string;
          total_score?: number;
          is_playing?: boolean;
          has_played?: boolean;
          position?: number;
        };
        Update: {
          id?: string;
          session_id?: string;
          spotify_id?: string;
          name?: string;
          artist?: string;
          album?: string;
          duration?: number;
          preview_url?: string | null;
          image_url?: string | null;
          added_by?: string;
          added_at?: string;
          total_score?: number;
          is_playing?: boolean;
          has_played?: boolean;
          position?: number;
        };
      };
      votes: {
        Row: {
          id: string;
          user_id: string;
          track_id: string;
          session_id: string;
          vote_type: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          track_id: string;
          session_id: string;
          vote_type: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          track_id?: string;
          session_id?: string;
          vote_type?: string;
          created_at?: string;
        };
      };
      chat_messages: {
        Row: {
          id: string;
          session_id: string;
          user_id: string;
          user_name: string;
          message: string;
          timestamp: string;
          is_moderated: boolean;
        };
        Insert: {
          id?: string;
          session_id: string;
          user_id: string;
          user_name: string;
          message: string;
          timestamp?: string;
          is_moderated?: boolean;
        };
        Update: {
          id?: string;
          session_id?: string;
          user_id?: string;
          user_name?: string;
          message?: string;
          timestamp?: string;
          is_moderated?: boolean;
        };
      };
    };
  };
}

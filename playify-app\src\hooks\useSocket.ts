import { useEffect, useRef } from 'react';
import { io, Socket } from 'socket.io-client';
import { useAppStore } from '@/store/useAppStore';
import { SocketEvents, VoteType, SpotifyTrack } from '@/types';

export const useSocket = () => {
  const socketRef = useRef<Socket<SocketEvents> | null>(null);
  const {
    setConnected,
    setCurrentSession,
    setPlaylist,
    addParticipant,
    removeParticipant,
    updateTrackVotes,
    addChatMessage,
    setError,
  } = useAppStore();

  useEffect(() => {
    // Initialize socket connection
    socketRef.current = io(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000', {
      path: '/api/socket',
    });

    const socket = socketRef.current;

    // Connection events
    socket.on('connect', () => {
      console.log('Connected to server');
      setConnected(true);
    });

    socket.on('disconnect', () => {
      console.log('Disconnected from server');
      setConnected(false);
    });

    // Session events
    socket.on('session-updated', (session) => {
      setCurrentSession(session);
    });

    socket.on('playlist-updated', (playlist) => {
      setPlaylist(playlist);
    });

    socket.on('user-joined', (user) => {
      addParticipant(user);
    });

    socket.on('user-left', (userId) => {
      removeParticipant(userId);
    });

    socket.on('track-voted', ({ trackId, votes, newScore }) => {
      updateTrackVotes(trackId, votes, newScore);
    });

    socket.on('chat-message', (message) => {
      addChatMessage(message);
    });

    socket.on('notification', ({ type, message }) => {
      // Handle notifications (could integrate with a toast system)
      console.log(`${type}: ${message}`);
    });

    socket.on('error', (error) => {
      console.error('Socket error:', error);
      setError(error);
    });

    return () => {
      socket.disconnect();
    };
  }, []);

  // Socket action methods
  const joinSession = (sessionCode: string, userName: string) => {
    socketRef.current?.emit('join-session', { sessionCode, userName });
  };

  const leaveSession = (sessionId: string, userId: string) => {
    socketRef.current?.emit('leave-session', { sessionId, userId });
  };

  const addTrack = (sessionId: string, track: SpotifyTrack, userId: string) => {
    socketRef.current?.emit('add-track', { sessionId, track, userId });
  };

  const voteTrack = (sessionId: string, trackId: string, userId: string, voteType: VoteType) => {
    socketRef.current?.emit('vote-track', { sessionId, trackId, userId, voteType });
  };

  const hostPlay = (sessionId: string) => {
    socketRef.current?.emit('host-play', { sessionId });
  };

  const hostPause = (sessionId: string) => {
    socketRef.current?.emit('host-pause', { sessionId });
  };

  const hostSkip = (sessionId: string) => {
    socketRef.current?.emit('host-skip', { sessionId });
  };

  const hostRemoveTrack = (sessionId: string, trackId: string) => {
    socketRef.current?.emit('host-remove-track', { sessionId, trackId });
  };

  const hostSetVolume = (sessionId: string, volume: number) => {
    socketRef.current?.emit('host-set-volume', { sessionId, volume });
  };

  const sendChatMessage = (sessionId: string, userId: string, message: string) => {
    socketRef.current?.emit('send-chat', { sessionId, userId, message });
  };

  return {
    socket: socketRef.current,
    joinSession,
    leaveSession,
    addTrack,
    voteTrack,
    hostPlay,
    hostPause,
    hostSkip,
    hostRemoveTrack,
    hostSetVolume,
    sendChatMessage,
  };
};

import { useEffect, useRef } from 'react';
import { useAppStore } from '@/store/useAppStore';
import { VoteType, SpotifyTrack } from '@/types';

export const useSocket = () => {
  const {
    setConnected,
    setCurrentSession,
    setPlaylist,
    addParticipant,
    removeParticipant,
    updateTrackVotes,
    addChatMessage,
    setError,
  } = useAppStore();

  useEffect(() => {
    // Simulate connection for now
    console.log('Socket connection simulated');
    setConnected(true);

    return () => {
      setConnected(false);
    };
  }, [setConnected]);

  // Mock socket action methods for now
  const joinSession = (sessionCode: string, userName: string) => {
    console.log('Mock: Joining session', sessionCode, userName);
    // Simulate successful join
    setTimeout(() => {
      addParticipant({
        id: `user_${Date.now()}`,
        displayName: userName,
        isHost: false,
        joinedAt: new Date(),
        points: 0,
        achievements: [],
      });
    }, 1000);
  };

  const leaveSession = (sessionId: string, userId: string) => {
    console.log('Mock: Leaving session', sessionId, userId);
  };

  const addTrack = (sessionId: string, track: SpotifyTrack, userId: string) => {
    console.log('Mock: Adding track', track.name);
  };

  const voteTrack = (sessionId: string, trackId: string, userId: string, voteType: VoteType) => {
    console.log('Mock: Voting on track', trackId, voteType);
  };

  const hostPlay = (sessionId: string) => {
    console.log('Mock: Host play', sessionId);
  };

  const hostPause = (sessionId: string) => {
    console.log('Mock: Host pause', sessionId);
  };

  const hostSkip = (sessionId: string) => {
    console.log('Mock: Host skip', sessionId);
  };

  const hostRemoveTrack = (sessionId: string, trackId: string) => {
    console.log('Mock: Host remove track', trackId);
  };

  const hostSetVolume = (sessionId: string, volume: number) => {
    console.log('Mock: Host set volume', volume);
  };

  const sendChatMessage = (sessionId: string, userId: string, message: string) => {
    console.log('Mock: Send chat message', message);
  };

  return {
    socket: null,
    joinSession,
    leaveSession,
    addTrack,
    voteTrack,
    hostPlay,
    hostPause,
    hostSkip,
    hostRemoveTrack,
    hostSetVolume,
    sendChatMessage,
  };
};

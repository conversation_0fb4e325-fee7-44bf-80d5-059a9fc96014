import { NextRequest } from 'next/server';
import { Server as NetServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import { supabase } from '@/lib/supabase';
import { generateId, calculateVoteScore } from '@/lib/utils';
import { SocketEvents, VoteType } from '@/types';

export const dynamic = 'force-dynamic';

// Extend the global object to store the socket server
declare global {
  var io: SocketIOServer | undefined;
}

const SocketHandler = (req: NextRequest, res: any) => {
  if (!global.io) {
    console.log('Initializing Socket.IO server...');
    
    const httpServer: NetServer = res.socket.server as any;
    global.io = new SocketIOServer(httpServer, {
      path: '/api/socket',
      addTrailingSlash: false,
      cors: {
        origin: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
        methods: ['GET', 'POST'],
      },
    });

    global.io.on('connection', (socket) => {
      console.log('Client connected:', socket.id);

      // Join session
      socket.on('join-session', async ({ sessionCode, userName }) => {
        try {
          // Get session
          const { data: session, error: sessionError } = await supabase
            .from('sessions')
            .select('*')
            .eq('code', sessionCode)
            .eq('is_active', true)
            .single();

          if (sessionError || !session) {
            socket.emit('error', 'Session not found');
            return;
          }

          if (session.is_locked) {
            socket.emit('error', 'Session is locked');
            return;
          }

          if (session.current_participants >= session.max_participants) {
            socket.emit('error', 'Session is full');
            return;
          }

          const userId = generateId();
          
          // Add participant
          const { error: participantError } = await supabase
            .from('participants')
            .insert({
              id: generateId(),
              session_id: session.id,
              user_id: userId,
              display_name: userName,
              is_host: false,
              points: 0,
              achievements: [],
            });

          if (participantError) {
            socket.emit('error', 'Failed to join session');
            return;
          }

          // Update participant count
          await supabase
            .from('sessions')
            .update({ current_participants: session.current_participants + 1 })
            .eq('id', session.id);

          // Join socket room
          socket.join(session.id);
          socket.data.sessionId = session.id;
          socket.data.userId = userId;

          // Notify user joined
          const newUser = {
            id: userId,
            displayName: userName,
            isHost: false,
            joinedAt: new Date(),
            points: 0,
            achievements: [],
          };

          socket.to(session.id).emit('user-joined', newUser);
          
          // Send session data to new user
          socket.emit('session-updated', {
            id: session.id,
            code: session.code,
            hostId: session.host_id,
            name: session.name,
            isActive: session.is_active,
            isLocked: session.is_locked,
            maxParticipants: session.max_participants,
            currentParticipants: session.current_participants + 1,
            createdAt: new Date(session.created_at),
            settings: session.settings,
          });

          // Get and send current playlist
          const { data: tracks } = await supabase
            .from('tracks')
            .select(`
              *,
              votes (*)
            `)
            .eq('session_id', session.id)
            .eq('has_played', false)
            .order('total_score', { ascending: false });

          if (tracks) {
            const playlist = {
              id: generateId(),
              sessionId: session.id,
              tracks: tracks.map(track => ({
                id: track.id,
                spotifyId: track.spotify_id,
                name: track.name,
                artist: track.artist,
                album: track.album,
                duration: track.duration,
                previewUrl: track.preview_url,
                imageUrl: track.image_url,
                addedBy: track.added_by,
                addedAt: new Date(track.added_at),
                votes: track.votes || [],
                totalScore: track.total_score,
                isPlaying: track.is_playing,
                hasPlayed: track.has_played,
              })),
              currentTrackIndex: 0,
              isPlaying: false,
              volume: 80,
              updatedAt: new Date(),
            };

            socket.emit('playlist-updated', playlist);
          }

        } catch (error) {
          console.error('Error joining session:', error);
          socket.emit('error', 'Failed to join session');
        }
      });

      // Leave session
      socket.on('leave-session', async ({ sessionId, userId }) => {
        try {
          // Remove participant
          await supabase
            .from('participants')
            .delete()
            .eq('session_id', sessionId)
            .eq('user_id', userId);

          // Update participant count
          const { data: session } = await supabase
            .from('sessions')
            .select('current_participants')
            .eq('id', sessionId)
            .single();

          if (session) {
            await supabase
              .from('sessions')
              .update({ current_participants: Math.max(0, session.current_participants - 1) })
              .eq('id', sessionId);
          }

          socket.leave(sessionId);
          socket.to(sessionId).emit('user-left', userId);
        } catch (error) {
          console.error('Error leaving session:', error);
        }
      });

      // Add track
      socket.on('add-track', async ({ sessionId, track, userId }) => {
        try {
          // Check for duplicates
          const { data: existingTrack } = await supabase
            .from('tracks')
            .select('id, total_score')
            .eq('session_id', sessionId)
            .eq('spotify_id', track.id)
            .eq('has_played', false)
            .single();

          if (existingTrack) {
            // Increase score instead of adding duplicate
            await supabase
              .from('tracks')
              .update({ total_score: existingTrack.total_score + 1 })
              .eq('id', existingTrack.id);

            socket.to(sessionId).emit('track-voted', {
              trackId: existingTrack.id,
              votes: [],
              newScore: existingTrack.total_score + 1,
            });
            return;
          }

          // Add new track
          const newTrack = {
            id: generateId(),
            session_id: sessionId,
            spotify_id: track.id,
            name: track.name,
            artist: track.artists.map(a => a.name).join(', '),
            album: track.album.name,
            duration: track.duration_ms,
            preview_url: track.preview_url,
            image_url: track.album.images[0]?.url,
            added_by: userId,
            total_score: 0,
            is_playing: false,
            has_played: false,
            position: 0,
          };

          const { data: insertedTrack, error } = await supabase
            .from('tracks')
            .insert(newTrack)
            .select()
            .single();

          if (error) {
            socket.emit('error', 'Failed to add track');
            return;
          }

          // Broadcast track added
          global.io?.to(sessionId).emit('track-added', {
            id: insertedTrack.id,
            spotifyId: insertedTrack.spotify_id,
            name: insertedTrack.name,
            artist: insertedTrack.artist,
            album: insertedTrack.album,
            duration: insertedTrack.duration,
            previewUrl: insertedTrack.preview_url,
            imageUrl: insertedTrack.image_url,
            addedBy: insertedTrack.added_by,
            addedAt: new Date(insertedTrack.added_at),
            votes: [],
            totalScore: insertedTrack.total_score,
            isPlaying: insertedTrack.is_playing,
            hasPlayed: insertedTrack.has_played,
          });

        } catch (error) {
          console.error('Error adding track:', error);
          socket.emit('error', 'Failed to add track');
        }
      });

      // Vote on track
      socket.on('vote-track', async ({ sessionId, trackId, userId, voteType }) => {
        try {
          // Remove existing vote from this user for this track
          await supabase
            .from('votes')
            .delete()
            .eq('user_id', userId)
            .eq('track_id', trackId);

          // Add new vote
          await supabase
            .from('votes')
            .insert({
              id: generateId(),
              user_id: userId,
              track_id: trackId,
              session_id: sessionId,
              vote_type: voteType,
            });

          // Get all votes for this track
          const { data: votes } = await supabase
            .from('votes')
            .select('vote_type')
            .eq('track_id', trackId);

          const newScore = calculateVoteScore(votes || []);

          // Update track score
          await supabase
            .from('tracks')
            .update({ total_score: newScore })
            .eq('id', trackId);

          // Broadcast vote update
          global.io?.to(sessionId).emit('track-voted', {
            trackId,
            votes: votes || [],
            newScore,
          });

        } catch (error) {
          console.error('Error voting on track:', error);
          socket.emit('error', 'Failed to vote on track');
        }
      });

      // Host controls
      socket.on('host-play', async ({ sessionId }) => {
        global.io?.to(sessionId).emit('playback-updated', {
          isPlaying: true,
          currentTrackIndex: 0,
          volume: 80,
        });
      });

      socket.on('host-pause', async ({ sessionId }) => {
        global.io?.to(sessionId).emit('playback-updated', {
          isPlaying: false,
          currentTrackIndex: 0,
          volume: 80,
        });
      });

      socket.on('host-skip', async ({ sessionId }) => {
        // Implementation for skipping tracks
        global.io?.to(sessionId).emit('playback-updated', {
          isPlaying: true,
          currentTrackIndex: 1,
          volume: 80,
        });
      });

      socket.on('host-remove-track', async ({ sessionId, trackId }) => {
        try {
          await supabase
            .from('tracks')
            .delete()
            .eq('id', trackId)
            .eq('session_id', sessionId);

          // Broadcast playlist update
          // This would trigger a playlist refresh
          global.io?.to(sessionId).emit('notification', {
            type: 'info',
            message: 'Track removed from playlist',
          });
        } catch (error) {
          console.error('Error removing track:', error);
        }
      });

      socket.on('disconnect', () => {
        console.log('Client disconnected:', socket.id);
        
        // Clean up user from session if they were in one
        if (socket.data.sessionId && socket.data.userId) {
          socket.to(socket.data.sessionId).emit('user-left', socket.data.userId);
        }
      });
    });
  }

  res.end();
};

export { SocketHandler as GET, SocketHandler as POST };

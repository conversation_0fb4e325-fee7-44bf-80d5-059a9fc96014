import { NextRequest, NextResponse } from 'next/server';

export const dynamic = 'force-dynamic';

// Simple placeholder for Socket.IO - will be implemented with a custom server
export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: 'Socket.IO endpoint - requires custom server setup',
    status: 'placeholder'
  });
}

export async function POST(request: NextRequest) {
  return NextResponse.json({
    message: 'Socket.IO endpoint - requires custom server setup',
    status: 'placeholder'
  });
}

// User and Session Types
export interface User {
  id: string;
  displayName: string;
  isHost: boolean;
  joinedAt: Date;
  points: number;
  achievements: string[];
}

export interface Session {
  id: string;
  code: string;
  hostId: string;
  name: string;
  isActive: boolean;
  isLocked: boolean;
  maxParticipants: number;
  currentParticipants: number;
  createdAt: Date;
  settings: SessionSettings;
}

export interface SessionSettings {
  allowChat: boolean;
  maxPlaylistLength: number;
  voteWeight: number;
  autoSkipThreshold: number;
  requireApproval: boolean;
  lowTrackThreshold: number;
}

// Music Types
export interface Track {
  id: string;
  spotifyId: string;
  name: string;
  artist: string;
  album: string;
  duration: number;
  previewUrl?: string;
  imageUrl?: string;
  addedBy: string;
  addedAt: Date;
  votes: Vote[];
  totalScore: number;
  isPlaying: boolean;
  hasPlayed: boolean;
}

export interface Vote {
  id: string;
  userId: string;
  trackId: string;
  type: VoteType;
  createdAt: Date;
}

export type VoteType = 'thumbs_up' | 'heart' | 'fire' | 'thumbs_down' | 'sleep';

export interface Playlist {
  id: string;
  sessionId: string;
  tracks: Track[];
  currentTrackIndex: number;
  isPlaying: boolean;
  volume: number;
  updatedAt: Date;
}

// Spotify API Types
export interface SpotifyTrack {
  id: string;
  name: string;
  artists: Array<{ name: string }>;
  album: {
    name: string;
    images: Array<{ url: string; height: number; width: number }>;
  };
  duration_ms: number;
  preview_url?: string;
  external_urls: {
    spotify: string;
  };
}

export interface SpotifySearchResponse {
  tracks: {
    items: SpotifyTrack[];
    total: number;
  };
}

// Chat Types
export interface ChatMessage {
  id: string;
  sessionId: string;
  userId: string;
  userName: string;
  message: string;
  timestamp: Date;
  isModerated: boolean;
}

// Socket Events
export interface SocketEvents {
  // Client to Server
  'join-session': (data: { sessionCode: string; userName: string }) => void;
  'leave-session': (data: { sessionId: string; userId: string }) => void;
  'add-track': (data: { sessionId: string; track: SpotifyTrack; userId: string }) => void;
  'vote-track': (data: { sessionId: string; trackId: string; userId: string; voteType: VoteType }) => void;
  'host-play': (data: { sessionId: string }) => void;
  'host-pause': (data: { sessionId: string }) => void;
  'host-skip': (data: { sessionId: string }) => void;
  'host-remove-track': (data: { sessionId: string; trackId: string }) => void;
  'host-set-volume': (data: { sessionId: string; volume: number }) => void;
  'send-chat': (data: { sessionId: string; userId: string; message: string }) => void;

  // Server to Client
  'session-updated': (session: Session) => void;
  'playlist-updated': (playlist: Playlist) => void;
  'user-joined': (user: User) => void;
  'user-left': (userId: string) => void;
  'track-added': (track: Track) => void;
  'track-voted': (data: { trackId: string; votes: Vote[]; newScore: number }) => void;
  'playback-updated': (data: { isPlaying: boolean; currentTrackIndex: number; volume: number }) => void;
  'chat-message': (message: ChatMessage) => void;
  'notification': (data: { type: 'info' | 'warning' | 'error'; message: string }) => void;
  'error': (error: string) => void;
}

// Component Props Types
export interface SessionCreatorProps {
  onSessionCreated: (session: Session) => void;
}

export interface PlaylistManagerProps {
  session: Session;
  playlist: Playlist;
  currentUser: User;
  onVote: (trackId: string, voteType: VoteType) => void;
  onAddTrack: (track: SpotifyTrack) => void;
}

export interface TrackSearchProps {
  onTrackSelect: (track: SpotifyTrack) => void;
  disabled?: boolean;
}

export interface VotingSystemProps {
  track: Track;
  currentUser: User;
  onVote: (voteType: VoteType) => void;
  disabled?: boolean;
}

export interface PlayerControlsProps {
  playlist: Playlist;
  isHost: boolean;
  onPlay: () => void;
  onPause: () => void;
  onSkip: () => void;
  onVolumeChange: (volume: number) => void;
  onRemoveTrack: (trackId: string) => void;
}

// Achievement Types
export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  condition: (user: User, session: Session) => boolean;
}

// Error Types
export interface AppError {
  code: string;
  message: string;
  details?: any;
}

import { SpotifySearchResponse, SpotifyTrack } from '@/types';

const SPOTIFY_API_BASE = 'https://api.spotify.com/v1';

// Get Spotify access token using client credentials flow
async function getAccessToken(): Promise<string> {
  const response = await fetch('/api/auth/spotify/token', {
    method: 'POST',
  });
  
  if (!response.ok) {
    throw new Error('Failed to get Spotify access token');
  }
  
  const data = await response.json();
  return data.access_token;
}

// Search for tracks on Spotify
export async function searchTracks(query: string, limit: number = 20): Promise<SpotifyTrack[]> {
  try {
    const token = await getAccessToken();
    
    const searchParams = new URLSearchParams({
      q: query,
      type: 'track',
      limit: limit.toString(),
      market: 'US',
    });
    
    const response = await fetch(`${SPOTIFY_API_BASE}/search?${searchParams}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      throw new Error('Failed to search tracks');
    }
    
    const data: SpotifySearchResponse = await response.json();
    return data.tracks.items;
  } catch (error) {
    console.error('Error searching tracks:', error);
    throw error;
  }
}

// Get track details by ID
export async function getTrack(trackId: string): Promise<SpotifyTrack> {
  try {
    const token = await getAccessToken();
    
    const response = await fetch(`${SPOTIFY_API_BASE}/tracks/${trackId}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      throw new Error('Failed to get track details');
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error getting track:', error);
    throw error;
  }
}

// Get multiple tracks by IDs
export async function getTracks(trackIds: string[]): Promise<SpotifyTrack[]> {
  try {
    const token = await getAccessToken();
    
    const response = await fetch(`${SPOTIFY_API_BASE}/tracks?ids=${trackIds.join(',')}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      throw new Error('Failed to get tracks');
    }
    
    const data = await response.json();
    return data.tracks;
  } catch (error) {
    console.error('Error getting tracks:', error);
    throw error;
  }
}

// Get track recommendations
export async function getRecommendations(
  seedTracks: string[],
  limit: number = 20
): Promise<SpotifyTrack[]> {
  try {
    const token = await getAccessToken();
    
    const searchParams = new URLSearchParams({
      seed_tracks: seedTracks.slice(0, 5).join(','), // Spotify allows max 5 seeds
      limit: limit.toString(),
      market: 'US',
    });
    
    const response = await fetch(`${SPOTIFY_API_BASE}/recommendations?${searchParams}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      throw new Error('Failed to get recommendations');
    }
    
    const data = await response.json();
    return data.tracks;
  } catch (error) {
    console.error('Error getting recommendations:', error);
    throw error;
  }
}

// Create a Spotify playlist (requires user authentication)
export async function createPlaylist(
  userId: string,
  name: string,
  trackUris: string[],
  accessToken: string
): Promise<{ id: string; external_urls: { spotify: string } }> {
  try {
    // Create playlist
    const createResponse = await fetch(`${SPOTIFY_API_BASE}/users/${userId}/playlists`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name,
        description: 'Created with Playify - Collaborative Music Playlist App',
        public: false,
      }),
    });
    
    if (!createResponse.ok) {
      throw new Error('Failed to create playlist');
    }
    
    const playlist = await createResponse.json();
    
    // Add tracks to playlist
    if (trackUris.length > 0) {
      const addTracksResponse = await fetch(`${SPOTIFY_API_BASE}/playlists/${playlist.id}/tracks`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          uris: trackUris,
        }),
      });
      
      if (!addTracksResponse.ok) {
        console.warn('Failed to add tracks to playlist');
      }
    }
    
    return {
      id: playlist.id,
      external_urls: playlist.external_urls,
    };
  } catch (error) {
    console.error('Error creating playlist:', error);
    throw error;
  }
}

// Convert track to Spotify URI
export function getSpotifyUri(trackId: string): string {
  return `spotify:track:${trackId}`;
}

// Extract track ID from Spotify URI or URL
export function extractTrackId(input: string): string | null {
  // Handle Spotify URI: spotify:track:4iV5W9uYEdYUVa79Axb7Rh
  if (input.startsWith('spotify:track:')) {
    return input.split(':')[2];
  }
  
  // Handle Spotify URL: https://open.spotify.com/track/4iV5W9uYEdYUVa79Axb7Rh
  const urlMatch = input.match(/spotify\.com\/track\/([a-zA-Z0-9]+)/);
  if (urlMatch) {
    return urlMatch[1];
  }
  
  // If it's already just an ID
  if (/^[a-zA-Z0-9]{22}$/.test(input)) {
    return input;
  }
  
  return null;
}

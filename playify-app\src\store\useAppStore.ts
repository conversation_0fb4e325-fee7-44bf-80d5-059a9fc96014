import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { Session, User, Playlist, Track, ChatMessage, VoteType } from '@/types';

interface AppState {
  // Session state
  currentSession: Session | null;
  currentUser: User | null;
  participants: User[];
  
  // Playlist state
  playlist: Playlist | null;
  currentTrack: Track | null;
  
  // Chat state
  chatMessages: ChatMessage[];
  
  // UI state
  isConnected: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setCurrentSession: (session: Session | null) => void;
  setCurrentUser: (user: User | null) => void;
  setParticipants: (participants: User[]) => void;
  addParticipant: (participant: User) => void;
  removeParticipant: (userId: string) => void;
  
  setPlaylist: (playlist: Playlist | null) => void;
  setCurrentTrack: (track: Track | null) => void;
  updateTrackVotes: (trackId: string, votes: any[], newScore: number) => void;
  
  addChatMessage: (message: ChatMessage) => void;
  setChatMessages: (messages: ChatMessage[]) => void;
  
  setConnected: (connected: boolean) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // Reset state
  reset: () => void;
}

const initialState = {
  currentSession: null,
  currentUser: null,
  participants: [],
  playlist: null,
  currentTrack: null,
  chatMessages: [],
  isConnected: false,
  isLoading: false,
  error: null,
};

export const useAppStore = create<AppState>()(
  devtools(
    (set, get) => ({
      ...initialState,
      
      setCurrentSession: (session) => set({ currentSession: session }),
      
      setCurrentUser: (user) => set({ currentUser: user }),
      
      setParticipants: (participants) => set({ participants }),
      
      addParticipant: (participant) => set((state) => ({
        participants: [...state.participants, participant]
      })),
      
      removeParticipant: (userId) => set((state) => ({
        participants: state.participants.filter(p => p.id !== userId)
      })),
      
      setPlaylist: (playlist) => {
        set({ playlist });
        if (playlist && playlist.tracks.length > 0) {
          const currentTrack = playlist.tracks.find(t => t.isPlaying) || playlist.tracks[playlist.currentTrackIndex];
          set({ currentTrack });
        } else {
          set({ currentTrack: null });
        }
      },
      
      setCurrentTrack: (track) => set({ currentTrack: track }),
      
      updateTrackVotes: (trackId, votes, newScore) => set((state) => {
        if (!state.playlist) return state;
        
        const updatedTracks = state.playlist.tracks.map(track => 
          track.id === trackId 
            ? { ...track, votes, totalScore: newScore }
            : track
        );
        
        return {
          playlist: {
            ...state.playlist,
            tracks: updatedTracks
          }
        };
      }),
      
      addChatMessage: (message) => set((state) => ({
        chatMessages: [...state.chatMessages, message]
      })),
      
      setChatMessages: (messages) => set({ chatMessages: messages }),
      
      setConnected: (connected) => set({ isConnected: connected }),
      
      setLoading: (loading) => set({ isLoading: loading }),
      
      setError: (error) => set({ error }),
      
      reset: () => set(initialState),
    }),
    {
      name: 'playify-store',
    }
  )
);

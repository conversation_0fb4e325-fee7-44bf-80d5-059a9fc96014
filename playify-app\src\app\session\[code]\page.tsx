'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Music, Users, Settings, QrCode, Play, Pause, SkipForward, Volume2 } from 'lucide-react';
import { useSocket } from '@/hooks/useSocket';
import { useAppStore } from '@/store/useAppStore';
import TrackSearch from '@/components/TrackSearch';
import { SpotifyTrack } from '@/types';

export default function SessionPage() {
  const params = useParams();
  const router = useRouter();
  const sessionCode = params.code as string;

  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');
  const [showQR, setShowQR] = useState(false);

  const {
    currentSession,
    currentUser,
    participants,
    playlist,
    isConnected,
    setCurrentUser,
    setCurrentSession,
  } = useAppStore();

  const {
    joinSession,
    hostPlay,
    hostPause,
    hostSkip,
    hostSetVolume,
  } = useSocket();

  useEffect(() => {
    // Load user data and join session
    const initializeSession = async () => {
      try {
        // Check if user is host (from localStorage)
        const storedSession = localStorage.getItem('playify_session');
        const storedUser = localStorage.getItem('playify_user');
        const tempUser = localStorage.getItem('playify_user_temp');

        let userData = null;
        let sessionData = null;

        if (storedSession && storedUser) {
          // Host user
          sessionData = JSON.parse(storedSession);
          userData = JSON.parse(storedUser);
        } else if (tempUser) {
          // Participant user
          const tempUserData = JSON.parse(tempUser);
          userData = {
            id: `user_${Date.now()}`,
            displayName: tempUserData.displayName,
            isHost: false,
            joinedAt: new Date(),
            points: 0,
            achievements: [],
          };
          localStorage.removeItem('playify_user_temp');
        }

        if (userData) {
          setCurrentUser(userData);

          // Join the session via socket
          joinSession(sessionCode, userData.displayName);
        } else {
          // No user data, redirect to join page
          router.push(`/join?code=${sessionCode}`);
        }

        if (sessionData) {
          setCurrentSession(sessionData);
        }

        // Generate QR code for session
        const qrUrl = await generateQRCode(`${window.location.origin}/join?code=${sessionCode}`);
        setQrCodeUrl(qrUrl);
      } catch (error) {
        console.error('Error initializing session:', error);
      }
    };

    initializeSession();
  }, [sessionCode, joinSession, setCurrentUser, setCurrentSession, router]);

  const generateQRCode = async (text: string): Promise<string> => {
    try {
      const QRCode = await import('qrcode');
      return QRCode.toDataURL(text, {
        width: 256,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF',
        },
      });
    } catch (error) {
      console.error('Error generating QR code:', error);
      return '';
    }
  };

  const handlePlayPause = () => {
    if (playlist?.isPlaying) {
      hostPause(currentSession?.id || '');
    } else {
      hostPlay(currentSession?.id || '');
    }
  };

  const handleSkip = () => {
    hostSkip(currentSession?.id || '');
  };

  const handleVolumeChange = (volume: number) => {
    hostSetVolume(currentSession?.id || '', volume);
  };

  const handleTrackSelect = (track: SpotifyTrack) => {
    if (currentSession && currentUser) {
      addTrack(currentSession.id, track, currentUser.id);
      // For demo purposes, add to local state
      console.log('Selected track:', track.name, 'by', track.artists.map(a => a.name).join(', '));
    }
  };

  if (!currentUser) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <div className="text-white text-xl">Loading...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white">
              {currentSession?.name || 'Session'}
            </h1>
            <p className="text-gray-300">
              Code: {sessionCode} • {isConnected ? 'Connected' : 'Disconnected'}
            </p>
          </div>

          <div className="flex items-center space-x-4">
            {currentUser.isHost && (
              <button
                onClick={() => setShowQR(!showQR)}
                className="bg-white/10 hover:bg-white/20 text-white p-3 rounded-xl transition-colors"
              >
                <QrCode className="h-6 w-6" />
              </button>
            )}
            <div className="flex items-center bg-white/10 rounded-xl px-4 py-2">
              <Users className="h-5 w-5 text-white mr-2" />
              <span className="text-white">{participants.length}</span>
            </div>
          </div>
        </div>

        {/* QR Code Modal */}
        {showQR && qrCodeUrl && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-2xl p-8 max-w-sm mx-4">
              <h3 className="text-xl font-bold text-center mb-4">Join Session</h3>
              <img src={qrCodeUrl} alt="QR Code" className="w-full mb-4" />
              <p className="text-center text-gray-600 mb-4">
                Scan this QR code or use code: <strong>{sessionCode}</strong>
              </p>
              <button
                onClick={() => setShowQR(false)}
                className="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 rounded-xl"
              >
                Close
              </button>
            </div>
          </div>
        )}

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Current Track */}
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20">
              <h2 className="text-xl font-semibold text-white mb-4 flex items-center">
                <Music className="h-5 w-5 mr-2" />
                Now Playing
              </h2>

              {playlist?.tracks && playlist.tracks.length > 0 ? (
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 bg-gray-600 rounded-lg flex items-center justify-center">
                    <Music className="h-8 w-8 text-gray-400" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-white font-semibold">
                      {playlist.tracks[0]?.name || 'No track selected'}
                    </h3>
                    <p className="text-gray-300">
                      {playlist.tracks[0]?.artist || 'Unknown artist'}
                    </p>
                  </div>

                  {currentUser.isHost && (
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={handlePlayPause}
                        className="bg-green-500 hover:bg-green-600 text-white p-3 rounded-full"
                      >
                        {playlist.isPlaying ? (
                          <Pause className="h-6 w-6" />
                        ) : (
                          <Play className="h-6 w-6" />
                        )}
                      </button>
                      <button
                        onClick={handleSkip}
                        className="bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-full"
                      >
                        <SkipForward className="h-6 w-6" />
                      </button>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Music className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-300">No tracks in playlist yet</p>
                  <p className="text-gray-400 text-sm">Add some tracks to get started!</p>
                </div>
              )}
            </div>

            {/* Playlist */}
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20">
              <h2 className="text-xl font-semibold text-white mb-4">
                Upcoming Tracks ({playlist?.tracks?.length || 0})
              </h2>

              {playlist?.tracks && playlist.tracks.length > 1 ? (
                <div className="space-y-3">
                  {playlist.tracks.slice(1, 6).map((track, index) => (
                    <div key={track.id} className="flex items-center space-x-4 p-3 bg-white/5 rounded-xl">
                      <div className="text-gray-400 font-mono text-sm w-6">
                        {index + 2}
                      </div>
                      <div className="w-12 h-12 bg-gray-600 rounded-lg flex items-center justify-center">
                        <Music className="h-6 w-6 text-gray-400" />
                      </div>
                      <div className="flex-1">
                        <h4 className="text-white font-medium">{track.name}</h4>
                        <p className="text-gray-300 text-sm">{track.artist}</p>
                      </div>
                      <div className="text-white font-semibold">
                        {track.totalScore > 0 ? `+${track.totalScore}` : track.totalScore}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-300">No upcoming tracks</p>
                </div>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-8">
            {/* Add Track */}
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
              <h3 className="text-lg font-semibold text-white mb-4">Add Track</h3>
              <TrackSearch
                onTrackSelect={handleTrackSelect}
                disabled={!currentUser || !currentSession}
              />
            </div>

            {/* Participants */}
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20">
              <h3 className="text-lg font-semibold text-white mb-4">
                Participants ({participants.length})
              </h3>
              <div className="space-y-2">
                {participants.map((participant) => (
                  <div key={participant.id} className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-sm font-semibold">
                        {participant.displayName.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <div className="flex-1">
                      <p className="text-white text-sm">
                        {participant.displayName}
                        {participant.isHost && (
                          <span className="ml-2 text-yellow-400 text-xs">HOST</span>
                        )}
                      </p>
                    </div>
                    <div className="text-gray-400 text-xs">
                      {participant.points} pts
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

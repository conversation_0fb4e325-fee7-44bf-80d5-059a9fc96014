'use client';

import { useState } from 'react';
import { Search, Plus, Play } from 'lucide-react';
import { SpotifyTrack } from '@/types';
import { formatDuration } from '@/lib/utils';

interface TrackSearchProps {
  onTrackSelect: (track: SpotifyTrack) => void;
  disabled?: boolean;
}

export default function TrackSearch({ onTrackSelect, disabled = false }: TrackSearchProps) {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SpotifyTrack[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSearch = async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setResults([]);
      return;
    }

    setIsSearching(true);
    setError(null);

    try {
      const response = await fetch(`/api/music/search?q=${encodeURIComponent(searchQuery)}&limit=10`);
      
      if (!response.ok) {
        throw new Error('Failed to search tracks');
      }

      const data = await response.json();
      setResults(data.tracks || []);
    } catch (err) {
      console.error('Search error:', err);
      setError('Failed to search tracks. Please try again.');
      setResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);
    
    // Debounce search
    const timeoutId = setTimeout(() => {
      handleSearch(value);
    }, 500);

    return () => clearTimeout(timeoutId);
  };

  const handleTrackSelect = (track: SpotifyTrack) => {
    onTrackSelect(track);
    setQuery('');
    setResults([]);
  };

  return (
    <div className="space-y-4">
      {/* Search Input */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
        <input
          type="text"
          placeholder="Search for songs, artists, or albums..."
          value={query}
          onChange={handleInputChange}
          disabled={disabled || isSearching}
          className="w-full pl-10 pr-4 py-3 rounded-xl bg-white/10 border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
        />
        {isSearching && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
          </div>
        )}
      </div>

      {/* Error Message */}
      {error && (
        <div className="text-red-400 text-sm bg-red-500/10 border border-red-500/20 rounded-xl p-3">
          {error}
        </div>
      )}

      {/* Search Results */}
      {results.length > 0 && (
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {results.map((track) => (
            <div
              key={track.id}
              className="flex items-center space-x-3 p-3 bg-white/5 hover:bg-white/10 rounded-xl transition-colors cursor-pointer group"
              onClick={() => handleTrackSelect(track)}
            >
              {/* Album Art */}
              <div className="w-12 h-12 bg-gray-600 rounded-lg overflow-hidden flex-shrink-0">
                {track.album.images[0] ? (
                  <img
                    src={track.album.images[0].url}
                    alt={track.album.name}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <Play className="h-6 w-6 text-gray-400" />
                  </div>
                )}
              </div>

              {/* Track Info */}
              <div className="flex-1 min-w-0">
                <h4 className="text-white font-medium truncate">{track.name}</h4>
                <p className="text-gray-300 text-sm truncate">
                  {track.artists.map(artist => artist.name).join(', ')}
                </p>
                <p className="text-gray-400 text-xs truncate">{track.album.name}</p>
              </div>

              {/* Duration */}
              <div className="text-gray-400 text-sm flex-shrink-0">
                {formatDuration(track.duration_ms)}
              </div>

              {/* Add Button */}
              <button
                className="opacity-0 group-hover:opacity-100 bg-green-500 hover:bg-green-600 text-white p-2 rounded-full transition-all duration-200"
                onClick={(e) => {
                  e.stopPropagation();
                  handleTrackSelect(track);
                }}
              >
                <Plus className="h-4 w-4" />
              </button>
            </div>
          ))}
        </div>
      )}

      {/* No Results */}
      {query && !isSearching && results.length === 0 && !error && (
        <div className="text-center py-8 text-gray-400">
          <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p>No tracks found for "{query}"</p>
          <p className="text-sm">Try different keywords or check your spelling</p>
        </div>
      )}

      {/* Instructions */}
      {!query && (
        <div className="text-center py-8 text-gray-400">
          <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p>Search for music to add to the playlist</p>
          <p className="text-sm">Start typing to see results</p>
        </div>
      )}
    </div>
  );
}

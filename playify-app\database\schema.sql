-- Playify Database Schema for Supabase

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Sessions table
CREATE TABLE sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code VARCHAR(6) UNIQUE NOT NULL,
    host_id VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    is_locked BOOLEAN DEFAULT false,
    max_participants INTEGER DEFAULT 50,
    current_participants INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    settings JSONB DEFAULT '{
        "allowChat": true,
        "maxPlaylistLength": 100,
        "voteWeight": 1,
        "autoSkipThreshold": -5,
        "requireApproval": false,
        "lowTrackThreshold": 5
    }'::jsonb
);

-- Participants table
CREATE TABLE participants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID REFERENCES sessions(id) ON DELETE CASCADE,
    user_id VARCHAR(255) NOT NULL,
    display_name VARCHAR(255) NOT NULL,
    is_host BOOLEAN DEFAULT false,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    points INTEGER DEFAULT 0,
    achievements TEXT[] DEFAULT '{}'
);

-- Tracks table
CREATE TABLE tracks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID REFERENCES sessions(id) ON DELETE CASCADE,
    spotify_id VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    artist VARCHAR(255) NOT NULL,
    album VARCHAR(255) NOT NULL,
    duration INTEGER NOT NULL, -- in milliseconds
    preview_url TEXT,
    image_url TEXT,
    added_by VARCHAR(255) NOT NULL,
    added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    total_score INTEGER DEFAULT 0,
    is_playing BOOLEAN DEFAULT false,
    has_played BOOLEAN DEFAULT false,
    position INTEGER DEFAULT 0
);

-- Votes table
CREATE TABLE votes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id VARCHAR(255) NOT NULL,
    track_id UUID REFERENCES tracks(id) ON DELETE CASCADE,
    session_id UUID REFERENCES sessions(id) ON DELETE CASCADE,
    vote_type VARCHAR(20) NOT NULL CHECK (vote_type IN ('thumbs_up', 'heart', 'fire', 'thumbs_down', 'sleep')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, track_id) -- One vote per user per track
);

-- Chat messages table
CREATE TABLE chat_messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID REFERENCES sessions(id) ON DELETE CASCADE,
    user_id VARCHAR(255) NOT NULL,
    user_name VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_moderated BOOLEAN DEFAULT false
);

-- Indexes for better performance
CREATE INDEX idx_sessions_code ON sessions(code);
CREATE INDEX idx_sessions_active ON sessions(is_active);
CREATE INDEX idx_participants_session ON participants(session_id);
CREATE INDEX idx_participants_user ON participants(user_id);
CREATE INDEX idx_tracks_session ON tracks(session_id);
CREATE INDEX idx_tracks_spotify ON tracks(spotify_id);
CREATE INDEX idx_tracks_score ON tracks(total_score DESC);
CREATE INDEX idx_votes_track ON votes(track_id);
CREATE INDEX idx_votes_user ON votes(user_id);
CREATE INDEX idx_chat_session ON chat_messages(session_id);
CREATE INDEX idx_chat_timestamp ON chat_messages(timestamp DESC);

-- Row Level Security (RLS) policies
ALTER TABLE sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE tracks ENABLE ROW LEVEL SECURITY;
ALTER TABLE votes ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;

-- Allow all operations for now (you can make these more restrictive based on your needs)
CREATE POLICY "Allow all operations on sessions" ON sessions FOR ALL USING (true);
CREATE POLICY "Allow all operations on participants" ON participants FOR ALL USING (true);
CREATE POLICY "Allow all operations on tracks" ON tracks FOR ALL USING (true);
CREATE POLICY "Allow all operations on votes" ON votes FOR ALL USING (true);
CREATE POLICY "Allow all operations on chat_messages" ON chat_messages FOR ALL USING (true);

-- Functions for automatic score calculation
CREATE OR REPLACE FUNCTION calculate_track_score(track_uuid UUID)
RETURNS INTEGER AS $$
DECLARE
    score INTEGER;
BEGIN
    SELECT COALESCE(SUM(
        CASE vote_type
            WHEN 'thumbs_up' THEN 1
            WHEN 'heart' THEN 2
            WHEN 'fire' THEN 3
            WHEN 'thumbs_down' THEN -1
            WHEN 'sleep' THEN -2
            ELSE 0
        END
    ), 0) INTO score
    FROM votes
    WHERE track_id = track_uuid;
    
    RETURN score;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update track score when votes change
CREATE OR REPLACE FUNCTION update_track_score()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        UPDATE tracks 
        SET total_score = calculate_track_score(NEW.track_id)
        WHERE id = NEW.track_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE tracks 
        SET total_score = calculate_track_score(OLD.track_id)
        WHERE id = OLD.track_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_track_score
    AFTER INSERT OR UPDATE OR DELETE ON votes
    FOR EACH ROW
    EXECUTE FUNCTION update_track_score();

-- Function to clean up old sessions (can be called periodically)
CREATE OR REPLACE FUNCTION cleanup_old_sessions()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM sessions 
    WHERE created_at < NOW() - INTERVAL '24 hours' 
    AND is_active = false;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;
